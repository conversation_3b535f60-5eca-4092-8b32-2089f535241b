-- Enhanced Chess Game Security Migration
-- This migration adds comprehensive security measures to prevent unauthorized game data modifications

-- ============================================================================
-- CHESS MOVE VALIDATION FUNCTIONS
-- ============================================================================

-- Function to validate if it's the player's turn
CREATE OR REPLACE FUNCTION validate_player_turn(
    game_id UUID,
    player_id UUID,
    current_fen TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    game_record RECORD;
    current_turn TEXT;
BEGIN
    -- Get current game state
    SELECT * INTO game_record FROM games WHERE id = game_id;
    
    -- Check if game exists and is active
    IF game_record IS NULL OR game_record.status != 'active' THEN
        RETURN FALSE;
    END IF;
    
    -- Extract whose turn it is from FEN (second part of FEN string)
    current_turn := split_part(current_fen, ' ', 2);
    
    -- Check if it's the player's turn
    IF (current_turn = 'w' AND player_id != game_record.white_player_id) OR
       (current_turn = 'b' AND player_id != game_record.black_player_id) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate move sequence integrity
CREATE OR REPLACE FUNCTION validate_move_sequence(
    old_moves TEXT[],
    new_moves TEXT[]
) RETURNS BOOLEAN AS $$
BEGIN
    -- New moves array should only add one move to the end
    IF array_length(new_moves, 1) != array_length(old_moves, 1) + 1 THEN
        RETURN FALSE;
    END IF;
    
    -- All previous moves should remain unchanged
    FOR i IN 1..array_length(old_moves, 1) LOOP
        IF old_moves[i] != new_moves[i] THEN
            RETURN FALSE;
        END IF;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate FEN format (basic validation)
CREATE OR REPLACE FUNCTION validate_fen_format(fen TEXT) RETURNS BOOLEAN AS $$
BEGIN
    -- Basic FEN validation: should have 6 parts separated by spaces
    IF array_length(string_to_array(fen, ' '), 1) != 6 THEN
        RETURN FALSE;
    END IF;
    
    -- Additional validations can be added here
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comprehensive move validation function
CREATE OR REPLACE FUNCTION validate_chess_move(
    game_id UUID,
    player_id UUID,
    old_moves TEXT[],
    new_moves TEXT[],
    old_fen TEXT,
    new_fen TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    -- Validate FEN formats
    IF NOT validate_fen_format(old_fen) OR NOT validate_fen_format(new_fen) THEN
        RETURN FALSE;
    END IF;
    
    -- Validate it's the player's turn
    IF NOT validate_player_turn(game_id, player_id, old_fen) THEN
        RETURN FALSE;
    END IF;
    
    -- Validate move sequence integrity
    IF NOT validate_move_sequence(old_moves, new_moves) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- ENHANCED ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Drop existing policies to replace with enhanced versions
DROP POLICY IF EXISTS "Players can update their games" ON public.games;
DROP POLICY IF EXISTS "Players can insert moves for their games" ON public.game_moves;

-- Enhanced game update policy with move validation
CREATE POLICY "Players can make valid moves" ON public.games
    FOR UPDATE USING (
        -- Must be a player in the game
        (auth.uid() = white_player_id OR auth.uid() = black_player_id) AND
        -- Game must be active for move updates
        (status = 'active' OR (status = 'waiting' AND auth.uid() = black_player_id))
    )
    WITH CHECK (
        -- Validate the move if it's a game state update
        CASE 
            WHEN OLD.moves != NEW.moves OR OLD.game_state != NEW.game_state THEN
                validate_chess_move(
                    NEW.id,
                    auth.uid(),
                    OLD.moves,
                    NEW.moves,
                    OLD.game_state,
                    NEW.game_state
                )
            ELSE TRUE
        END AND
        -- Prevent modification of critical game metadata during moves
        OLD.white_player_id = NEW.white_player_id AND
        OLD.black_player_id = NEW.black_player_id AND
        OLD.created_at = NEW.created_at AND
        -- Only allow specific status transitions
        (
            (OLD.status = 'waiting' AND NEW.status IN ('active', 'abandoned')) OR
            (OLD.status = 'active' AND NEW.status IN ('active', 'completed', 'abandoned')) OR
            (OLD.status = NEW.status)
        )
    );

-- Enhanced game moves policy with validation
CREATE POLICY "Players can record valid moves" ON public.game_moves
    FOR INSERT WITH CHECK (
        -- Must be the player making the move
        auth.uid() = player_id AND
        -- Must be a player in the game
        EXISTS (
            SELECT 1 FROM public.games
            WHERE games.id = game_moves.game_id
            AND (games.white_player_id = auth.uid() OR games.black_player_id = auth.uid())
            AND games.status = 'active'
        ) AND
        -- Move number should be sequential
        move_number = (
            SELECT COALESCE(MAX(move_number), 0) + 1
            FROM public.game_moves gm
            WHERE gm.game_id = game_moves.game_id
        )
    );

-- ============================================================================
-- COLUMN-LEVEL SECURITY FUNCTIONS
-- ============================================================================

-- Function to check if user can update specific game columns
CREATE OR REPLACE FUNCTION can_update_game_column(
    column_name TEXT,
    game_id UUID,
    player_id UUID,
    old_status TEXT,
    new_status TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    CASE column_name
        WHEN 'moves', 'game_state', 'updated_at' THEN
            -- Move-related columns: only during active games
            RETURN old_status = 'active';
        WHEN 'status' THEN
            -- Status changes: specific transitions only
            RETURN (
                (old_status = 'waiting' AND new_status IN ('active', 'abandoned')) OR
                (old_status = 'active' AND new_status IN ('completed', 'abandoned'))
            );
        WHEN 'winner', 'result_reason', 'completed_at' THEN
            -- Game completion columns: only when completing game
            RETURN old_status = 'active' AND new_status = 'completed';
        WHEN 'black_player_id' THEN
            -- Joining game: only when game is waiting
            RETURN old_status = 'waiting' AND new_status = 'active';
        ELSE
            -- Other columns: generally not updatable during gameplay
            RETURN FALSE;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- AUDIT AND LOGGING
-- ============================================================================

-- Create audit table for game changes
CREATE TABLE IF NOT EXISTS public.game_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    game_id UUID NOT NULL REFERENCES public.games(id) ON DELETE CASCADE,
    player_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    old_data JSONB,
    new_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on audit table
ALTER TABLE public.game_audit_log ENABLE ROW LEVEL SECURITY;

-- Audit table policy (only players can view their game audits)
CREATE POLICY "Players can view game audit logs" ON public.game_audit_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.games
            WHERE games.id = game_audit_log.game_id
            AND (games.white_player_id = auth.uid() OR games.black_player_id = auth.uid())
        )
    );

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_game_changes()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.game_audit_log (
        game_id,
        player_id,
        action,
        old_data,
        new_data
    ) VALUES (
        NEW.id,
        auth.uid(),
        TG_OP,
        CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE to_jsonb(OLD) END,
        CASE WHEN TG_OP = 'DELETE' THEN NULL ELSE to_jsonb(NEW) END
    );
    
    RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit trigger
DROP TRIGGER IF EXISTS games_audit_trigger ON public.games;
CREATE TRIGGER games_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.games
    FOR EACH ROW
    EXECUTE FUNCTION audit_game_changes();

-- ============================================================================
-- RATE LIMITING
-- ============================================================================

-- Create rate limiting table
CREATE TABLE IF NOT EXISTS public.player_rate_limits (
    player_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    game_id UUID NOT NULL REFERENCES public.games(id) ON DELETE CASCADE,
    last_move_at TIMESTAMPTZ DEFAULT NOW(),
    move_count INTEGER DEFAULT 0,
    window_start TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (player_id, game_id)
);

-- Enable RLS on rate limits table
ALTER TABLE public.player_rate_limits ENABLE ROW LEVEL SECURITY;

-- Rate limits policy
CREATE POLICY "Players can view own rate limits" ON public.player_rate_limits
    FOR ALL USING (auth.uid() = player_id);

-- Rate limiting function (prevents rapid-fire moves)
CREATE OR REPLACE FUNCTION check_move_rate_limit(
    p_player_id UUID,
    p_game_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    rate_record RECORD;
    current_time TIMESTAMPTZ := NOW();
    min_move_interval INTERVAL := '1 second';
    max_moves_per_minute INTEGER := 30;
BEGIN
    -- Get or create rate limit record
    SELECT * INTO rate_record
    FROM public.player_rate_limits
    WHERE player_id = p_player_id AND game_id = p_game_id;
    
    IF rate_record IS NULL THEN
        -- First move, create record
        INSERT INTO public.player_rate_limits (player_id, game_id, last_move_at, move_count, window_start)
        VALUES (p_player_id, p_game_id, current_time, 1, current_time);
        RETURN TRUE;
    END IF;
    
    -- Check minimum interval between moves
    IF current_time - rate_record.last_move_at < min_move_interval THEN
        RETURN FALSE;
    END IF;
    
    -- Reset window if more than 1 minute has passed
    IF current_time - rate_record.window_start > INTERVAL '1 minute' THEN
        UPDATE public.player_rate_limits
        SET move_count = 1, window_start = current_time, last_move_at = current_time
        WHERE player_id = p_player_id AND game_id = p_game_id;
        RETURN TRUE;
    END IF;
    
    -- Check moves per minute limit
    IF rate_record.move_count >= max_moves_per_minute THEN
        RETURN FALSE;
    END IF;
    
    -- Update rate limit record
    UPDATE public.player_rate_limits
    SET move_count = move_count + 1, last_move_at = current_time
    WHERE player_id = p_player_id AND game_id = p_game_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add rate limiting to the move validation
CREATE OR REPLACE FUNCTION validate_chess_move_with_rate_limit(
    game_id UUID,
    player_id UUID,
    old_moves TEXT[],
    new_moves TEXT[],
    old_fen TEXT,
    new_fen TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    -- Check rate limit first
    IF NOT check_move_rate_limit(player_id, game_id) THEN
        RETURN FALSE;
    END IF;
    
    -- Then validate the move
    RETURN validate_chess_move(game_id, player_id, old_moves, new_moves, old_fen, new_fen);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the policy to use rate-limited validation
DROP POLICY IF EXISTS "Players can make valid moves" ON public.games;
CREATE POLICY "Players can make valid moves" ON public.games
    FOR UPDATE USING (
        (auth.uid() = white_player_id OR auth.uid() = black_player_id) AND
        (status = 'active' OR (status = 'waiting' AND auth.uid() = black_player_id))
    )
    WITH CHECK (
        CASE 
            WHEN OLD.moves != NEW.moves OR OLD.game_state != NEW.game_state THEN
                validate_chess_move_with_rate_limit(
                    NEW.id,
                    auth.uid(),
                    OLD.moves,
                    NEW.moves,
                    OLD.game_state,
                    NEW.game_state
                )
            ELSE TRUE
        END AND
        OLD.white_player_id = NEW.white_player_id AND
        OLD.black_player_id = NEW.black_player_id AND
        OLD.created_at = NEW.created_at AND
        (
            (OLD.status = 'waiting' AND NEW.status IN ('active', 'abandoned')) OR
            (OLD.status = 'active' AND NEW.status IN ('active', 'completed', 'abandoned')) OR
            (OLD.status = NEW.status)
        )
    );

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for audit table
CREATE INDEX IF NOT EXISTS idx_game_audit_log_game_id ON public.game_audit_log(game_id);
CREATE INDEX IF NOT EXISTS idx_game_audit_log_player_id ON public.game_audit_log(player_id);
CREATE INDEX IF NOT EXISTS idx_game_audit_log_created_at ON public.game_audit_log(created_at);

-- Indexes for rate limiting
CREATE INDEX IF NOT EXISTS idx_player_rate_limits_last_move ON public.player_rate_limits(last_move_at);
CREATE INDEX IF NOT EXISTS idx_player_rate_limits_window_start ON public.player_rate_limits(window_start);

-- ============================================================================
-- COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION validate_chess_move_with_rate_limit IS 'Validates chess moves with rate limiting to prevent abuse';
COMMENT ON FUNCTION validate_player_turn IS 'Ensures only the correct player can make moves based on FEN turn indicator';
COMMENT ON FUNCTION validate_move_sequence IS 'Prevents tampering with move history by ensuring sequential move additions';
COMMENT ON TABLE public.game_audit_log IS 'Audit trail for all game modifications';
COMMENT ON TABLE public.player_rate_limits IS 'Rate limiting to prevent rapid-fire move abuse';
