# Chess Game Security Migration

This directory contains database migrations for enhanced security in the chess web application.

## Migration: 20250124_enhanced_game_security.sql

This migration adds comprehensive security measures to prevent unauthorized game data modifications while maintaining the performance and real-time capabilities of the chess application.

### Features Added

#### 1. **Chess Move Validation Functions**
- `validate_player_turn()` - Ensures only the correct player can move based on FEN turn indicator
- `validate_move_sequence()` - Prevents tampering with move history
- `validate_fen_format()` - Basic FEN string validation
- `validate_chess_move()` - Comprehensive move validation
- `validate_chess_move_with_rate_limit()` - Move validation with rate limiting

#### 2. **Enhanced Row Level Security (RLS) Policies**
- **Games Table**: Strict policies preventing unauthorized updates
- **Game Moves Table**: Sequential move validation
- **Column-level restrictions**: Only specific columns can be updated in specific game states

#### 3. **Audit and Logging System**
- `game_audit_log` table tracks all game modifications
- Automatic audit trail for compliance and debugging
- Player-specific audit access through RLS

#### 4. **Rate Limiting System**
- `player_rate_limits` table prevents rapid-fire move abuse
- Configurable limits: 1 second minimum between moves, 30 moves per minute maximum
- Automatic window reset and cleanup

#### 5. **Performance Optimizations**
- Strategic indexes on audit and rate limiting tables
- Efficient query patterns for real-time operations

### Security Benefits

#### **Prevents Common Attack Vectors:**
1. **Move Tampering**: Players cannot modify previous moves or make invalid sequences
2. **Turn Skipping**: Only the correct player can move based on game state
3. **Rapid-Fire Abuse**: Rate limiting prevents overwhelming the system
4. **Status Manipulation**: Strict state transition rules
5. **Metadata Corruption**: Critical game data is protected from modification

#### **Maintains Performance:**
- All validation happens at the database level (fastest possible)
- Compatible with Supabase real-time subscriptions
- Minimal overhead for legitimate moves

### How to Apply

#### Option 1: Using Supabase CLI (Recommended)
```bash
# If you have Supabase CLI installed
supabase db reset
# or
supabase migration up
```

#### Option 2: Manual Application
1. Connect to your Supabase database
2. Run the SQL file contents in the SQL editor
3. Verify all functions and policies are created

### Testing the Migration

After applying the migration, test these scenarios:

#### **Valid Operations (Should Work):**
```javascript
// Player making a move on their turn
await supabase
  .from('games')
  .update({
    game_state: 'new_fen_after_move',
    moves: [...existing_moves, 'e4'],
    updated_at: new Date().toISOString()
  })
  .eq('id', gameId)
```

#### **Invalid Operations (Should Fail):**
```javascript
// Trying to modify previous moves
await supabase
  .from('games')
  .update({
    moves: ['modified_first_move', 'e4'] // Should fail
  })
  .eq('id', gameId)

// Moving when it's not your turn
// Should fail based on FEN turn indicator

// Rapid-fire moves (faster than 1 second apart)
// Should fail due to rate limiting
```

### Configuration

You can adjust the rate limiting parameters by modifying these values in the `check_move_rate_limit` function:

```sql
min_move_interval INTERVAL := '1 second';      -- Minimum time between moves
max_moves_per_minute INTEGER := 30;            -- Maximum moves per minute
```

### Monitoring

#### **Check Audit Logs:**
```sql
SELECT * FROM game_audit_log 
WHERE game_id = 'your-game-id' 
ORDER BY created_at DESC;
```

#### **Monitor Rate Limits:**
```sql
SELECT * FROM player_rate_limits 
WHERE player_id = 'your-player-id';
```

#### **View Failed Attempts:**
Check your Supabase logs for RLS policy violations, which indicate blocked unauthorized attempts.

### Rollback

If you need to rollback this migration:

1. **Remove the new policies:**
```sql
DROP POLICY IF EXISTS "Players can make valid moves" ON public.games;
DROP POLICY IF EXISTS "Players can record valid moves" ON public.game_moves;
```

2. **Restore original policies:**
```sql
CREATE POLICY "Players can update their games" ON public.games
    FOR UPDATE USING (
        auth.uid() = white_player_id OR
        auth.uid() = black_player_id
    );
```

3. **Drop new tables and functions:**
```sql
DROP TABLE IF EXISTS public.game_audit_log;
DROP TABLE IF EXISTS public.player_rate_limits;
DROP FUNCTION IF EXISTS validate_chess_move_with_rate_limit;
-- ... (drop other functions as needed)
```

### Performance Impact

- **Minimal overhead** for legitimate moves (< 1ms additional processing)
- **Significant protection** against abuse and cheating
- **Compatible** with existing real-time subscriptions
- **Scalable** design that works with high-traffic games

### Support

If you encounter issues with this migration:

1. Check Supabase logs for specific error messages
2. Verify your game state follows standard FEN format
3. Ensure move sequences are properly incremental
4. Test rate limiting with appropriate delays between moves

The migration is designed to be backward-compatible with existing game functionality while adding robust security measures.
